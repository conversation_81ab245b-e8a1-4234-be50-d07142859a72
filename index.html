<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jana Breitmar - Tiefe • Flow • Purpose</title>
    <link rel="icon" type="image/png" href="bilder/favicon.png">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="main-title">JANA BREITMAR</h1>
                <h2 class="subtitle">
                    Tiefe <span class="circle">•</span> Beziehung <span class="circle">•</span> Purpose
                </h2>
            </div>
            <div class="hero-image">
                <img src="bilder/blumenfeld.png" alt="Blumenfeld" />
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator" id="scroll-indicator">
            <div class="scroll-arrow"></div>
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- Retreat Section -->
    <section class="retreat-preview-section">
        <div class="container">
            <div class="retreat-preview-content">
                <div class="retreat-preview-text">
                    <h2 class="section-title">Co-Kreatives Retreat</h2>
                    <p class="section-description">
                        Für tiefe Zusammenarbeit und authentische Verbindung. Ein Raum für gemeinsames Wachstum und kreative Entfaltung.
                    </p>
                    <a href="cokreativesretreat.html" class="retreat-cta-button">
                        <span>Mehr erfahren</span>
                        <span class="button-icon">🌱</span>
                    </a>
                </div>
                <div class="retreat-preview-visual">
                    <div class="retreat-image">
                        <img src="bilder/retreat/zusammenhalt.png" alt="Co-Kreatives Retreat - Zusammenhalt" />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Music Section -->
    <section class="music-preview-section">
        <div class="container">
            <div class="music-preview-content">
                <div class="music-preview-text">
                    <h2 class="section-title">Meine Musik</h2>
                    <p class="section-description">
                        In den Tiefen meines persönlichen Entwicklungsprozesses sind eine Menge Lieder entstanden, die du dir hier anhören kannst.
                    </p>
                    <a href="musik.html" class="music-cta-button">
                        <span>Musik entdecken</span>
                        <span class="button-icon">🎵</span>
                    </a>
                </div>
                <div class="music-preview-visual">
                    <div class="music-player-preview">
                        <div class="preview-album-cover" id="preview-album-cover">
                            <img src="bilder/album-covers/Like Water.png" alt="Like Water Album Cover" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="preview-cover-placeholder" style="display: none;">🎵</div>
                        </div>
                        <div class="preview-controls">
                            <button class="preview-button" id="preview-play-button">
                                <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8 5v14l11-7z"/>
                                </svg>
                                <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                                </svg>
                            </button>
                            <div class="preview-progress" id="preview-progress">
                                <div class="preview-progress-fill" id="preview-progress-fill"></div>
                            </div>
                        </div>
                        <div class="preview-song-info" id="preview-song-info">
                            <div class="preview-song-title">Like Water</div>
                            <div class="preview-artist">Jana Breitmar</div>
                        </div>
                        <audio id="preview-audio" preload="metadata">
                            <source src="audio/Like Water.wav" type="audio/wav">
                        </audio>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="header-and-footer.js"></script>
    <script src="music-preview.js"></script>
    <script src="scroll-indicator.js"></script>
</body>
</html>
